package com.enosisbd.api.server.service.googlesheets.impl;

import com.enosisbd.api.server.dto.*;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.exception.ForbiddenRestException;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.oauth.GoogleTokenService;
import com.enosisbd.api.server.service.user.UserService;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.SheetsRequestInitializer;
import com.google.api.services.sheets.v4.SheetsScopes;
import com.google.api.services.sheets.v4.model.BatchGetValuesResponse;
import com.google.api.services.sheets.v4.model.Sheet;
import com.google.api.services.sheets.v4.model.Spreadsheet;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Log4j2
public class GoogleSheetsServiceImpl implements GoogleSheetsService {

    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final List<String> SCOPES = Collections.singletonList(
            SheetsScopes.SPREADSHEETS_READONLY
    );
    private static final String APPLICATION_NAME = "Enosis Portal Google Sheets Integration";

    private final GoogleTokenService googleTokenService;
    private final UserService userService;

    @Value("${app.google.api.key:}")
    private String googleApiKey;

    @Autowired(required = false)
    private ClientRegistrationRepository clientRegistrationRepository;

    @Override
    public String extractSheetId(String sheetUrl) {
        // Extract the sheet ID from the URL
        // Example URL: https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit
        Pattern pattern = Pattern.compile("/spreadsheets/d/([a-zA-Z0-9-_]+)");
        Matcher matcher = pattern.matcher(sheetUrl);

        if (matcher.find()) {
            return matcher.group(1);
        }

        throw new BadRequestRestException("Invalid Google Sheets URL format");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<List<Object>> getSheetData(String sheetId, String sheetName, String userEmail) throws IOException, GeneralSecurityException {
        Sheets service = getSheetService(userEmail);
        return service.spreadsheets().values()
                .get(sheetId, sheetName)
                .execute()
                .getValues();
    }

    @Override
    public boolean hasAccessToSheet(String sheetId, String userEmail) throws IOException, GeneralSecurityException {
        if (StringUtils.isEmpty(userEmail)) {
            // If no user email is provided, we'll use the google API key which can only access public sheets
            log.warn("No user email provided for access check, using service account");
            return true;
        }

        try {
            // Try to access the sheet's metadata to check permissions
            // If we can get the spreadsheet info, the user has access
            log.info("Checking if user {} has access to sheet {}", userEmail, sheetId);
            Sheets service = getSheetService(userEmail);
            service.spreadsheets().get(sheetId)
                    .setFields("spreadsheetId") // Minimal fields to reduce response size
                    .execute();
            log.info("User {} has access to sheet {}", userEmail, sheetId);
            return true;
        } catch (Exception e) {
            log.error("Error checking access to sheet for user {}: {}", userEmail, e.getMessage());
            return false;
        }
    }

    @Override
    public SubmoduleExtractionResultDto extractSubmodulesWithValidation(
            List<List<Object>> sheetData,
            int columnIndex,
            int startRow,
            String sheetName,
            String columnLetter) {

        List<SubModuleRangeDto> submodules = new ArrayList<>();
        List<SubmoduleValidationIssueDto> validationIssues = new ArrayList<>();

        if (sheetData == null || sheetData.isEmpty() || startRow >= sheetData.size()) {
            return SubmoduleExtractionResultDto.builder()
                    .submodules(submodules)
                    .validationIssues(validationIssues)
                    .build();
        }

        String currentSubmoduleName = null;
        int currentStartRow = -1;
        int emptySubmoduleCounter = 1;

        // Process rows starting from the specified start row
        for (int rowIndex = startRow; rowIndex < sheetData.size(); rowIndex++) {
            List<Object> row = sheetData.get(rowIndex);

            // Skip rows that don't have enough columns
            if (row.size() <= columnIndex) {
                continue;
            }

            Object cellValue = row.get(columnIndex);
            String cellValueStr = cellValue != null ? cellValue.toString().trim() : "";

            // If we find a non-empty cell in the specified column
            if (!cellValueStr.isEmpty()) {
                // If we already have a current submodule, add it to the list
                if (currentSubmoduleName != null) {
                    int currentEndRow = rowIndex - 1;
                    String range = (currentStartRow + 1) + "-" + (currentEndRow + 1); // Convert to 1-based indices for display

                    submodules.add(SubModuleRangeDto.builder()
                            .name(currentSubmoduleName)
                            .range(range)
                            .startRow(currentStartRow)
                            .endRow(currentEndRow)
                            .build());
                }

                // Validate and potentially correct the submodule name
                String originalName = cellValueStr;
                String correctedName = originalName;

                if (originalName.length() < 3) {
                    // Auto-correct with standardized placeholder
                    correctedName = String.format("Empty Submodule %02d", emptySubmoduleCounter++);

                    // Create validation issue
                    SubmoduleValidationIssueDto issue = SubmoduleValidationIssueDto.builder()
                            .originalName(originalName)
                            .correctedName(correctedName)
                            .sheetName(sheetName)
                            .columnLetter(columnLetter)
                            .rowNumber(rowIndex + 1) // Convert to 1-based for user display
                            .cellReference(columnLetter + (rowIndex + 1))
                            .issueType("NAME_TOO_SHORT")
                            .description(String.format("Submodule name '%s' is too short (minimum 3 characters). Auto-corrected to '%s'.",
                                    originalName, correctedName))
                            .build();

                    validationIssues.add(issue);
                }

                // Start a new submodule with the corrected name
                currentSubmoduleName = correctedName;
                currentStartRow = rowIndex;
            }
        }

        // Add the last submodule if there is one
        if (currentSubmoduleName != null) {
            int currentEndRow = sheetData.size() - 1;
            String range = (currentStartRow + 1) + "-" + (currentEndRow + 1); // Convert to 1-based indices for display

            submodules.add(SubModuleRangeDto.builder()
                    .name(currentSubmoduleName)
                    .range(range)
                    .startRow(currentStartRow)
                    .endRow(currentEndRow)
                    .build());
        }

        return SubmoduleExtractionResultDto.builder()
                .submodules(submodules)
                .validationIssues(validationIssues)
                .build();
    }

    @Override
    public int columnLetterToIndex(String column) {
        if (column == null || column.isEmpty()) {
            throw new BadRequestRestException("Column letter cannot be null or empty");
        }

        column = column.toUpperCase();
        if (!column.matches("^[A-Z]$")) {
            throw new BadRequestRestException("Column must be a single letter (A-Z)");
        }

        // Convert column letter to 0-based index (A=0, B=1, etc.)
        return column.charAt(0) - 'A';
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<SheetTestCaseDto> getTestCases(
            String sheetId,
            String sheetName,
            int startRow,
            int endRow,
            String caseIdColumn,
            String descriptionColumn,
            String expectedResultColumn,
            String userEmail) throws IOException, GeneralSecurityException {

        // Check if the user has access to the sheet
        if (userEmail != null && !hasAccessToSheet(sheetId, userEmail)) {
            throw new ForbiddenRestException("You don't have access to this Google Sheet");
        }

        // Get sheet data
        List<List<Object>> sheetData = getSheetData(sheetId, sheetName, userEmail);

        // Convert column letters to indices
        int caseIdColumnIndex = columnLetterToIndex(caseIdColumn);
        int descriptionColumnIndex = columnLetterToIndex(descriptionColumn);
        int expectedResultColumnIndex = columnLetterToIndex(expectedResultColumn);

        // Validate row range
        if (startRow < 0) {
            startRow = 0;
        }

        if (endRow >= sheetData.size()) {
            endRow = sheetData.size() - 1;
        }

        if (startRow > endRow) {
            throw new BadRequestRestException("Invalid row range: start row cannot be greater than end row");
        }

        // Extract test cases from the specified range
        List<SheetTestCaseDto> testCases = new ArrayList<>();

        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            List<Object> row = sheetData.get(rowIndex);

            // Skip rows that don't have enough columns
            int maxColumnIndex = Math.max(Math.max(caseIdColumnIndex, descriptionColumnIndex), expectedResultColumnIndex);
            if (row.size() <= maxColumnIndex) {
                continue;
            }

            // Extract values from the row
            String caseId = getCellValueAsString(row, caseIdColumnIndex);
            String description = getCellValueAsString(row, descriptionColumnIndex);
            String expectedResult = getCellValueAsString(row, expectedResultColumnIndex);

            // Create test case DTO
            SheetTestCaseDto testCase = SheetTestCaseDto.builder()
                    .caseId(caseId)
                    .description(description)
                    .expectedResult(expectedResult)
                    .build();

            testCases.add(testCase);
        }

        return testCases;
    }

    /**
     * Get cell value as string, handling null values
     *
     * @param row The row data
     * @param columnIndex The column index
     * @return The cell value as string, or empty string if null
     */
    private String getCellValueAsString(List<Object> row, int columnIndex) {
        if (columnIndex >= row.size()) {
            return "";
        }

        Object cellValue = row.get(columnIndex);
        return cellValue != null ? cellValue.toString().trim() : "";
    }

    private Sheets getSheetService(String userEmail) throws IOException, GeneralSecurityException {
        NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();

        if (userEmail == null) {
            throw new IllegalArgumentException("User email is required for Google Sheets access. User must be authenticated.");
        }

        // Check if user is a Google OAuth user or local user
        User user = userService.findByEmail(userEmail).orElse(null);
        if (user == null) {
            throw new IllegalArgumentException("User not found: " + userEmail);
        }

        // Try OAuth first for Google users
        if ("GOOGLE".equalsIgnoreCase(user.getProvider())) {
            log.info("Attempting to use Google OAuth credentials for user: {}", userEmail);

            GoogleCredentials userCredentials = googleTokenService.getValidCredentials(userEmail);
            if (userCredentials != null) {
                log.info("Successfully obtained user OAuth credentials for user: {}", userEmail);
                HttpRequestInitializer requestInitializer = new HttpCredentialsAdapter(userCredentials);

                return new Sheets.Builder(httpTransport, JSON_FACTORY, requestInitializer)
                        .setApplicationName(APPLICATION_NAME)
                        .build();
            } else {
                log.warn("No valid OAuth tokens found for Google user: {}", userEmail);
            }
        }

        // For local users or when OAuth fails, use API key for public sheet access
        if (googleApiKey == null || googleApiKey.trim().isEmpty()) {
            throw new IllegalStateException("Google API key not configured. " +
                    "Local users need API key to access public Google Sheets. " +
                    "Set app.google.api.key environment variable or user must log in via Google OAuth.");
        }

        log.info("Using Google API key for public sheet access for user: {}", userEmail);

        return new Sheets.Builder(httpTransport, JSON_FACTORY, null)
                .setApplicationName(APPLICATION_NAME)
                .setGoogleClientRequestInitializer(new SheetsRequestInitializer(googleApiKey))
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public SpreadsheetBatchResponseDto getAllSheetsDataBatch(String sheetId, String userEmail) throws IOException, GeneralSecurityException {
        log.info("Starting batch retrieval of all sheets data for spreadsheet: {}", sheetId);

        // Check if the user has access to the sheet
        if (userEmail != null && !hasAccessToSheet(sheetId, userEmail)) {
            throw new ForbiddenRestException("You don't have access to this Google Sheet");
        }

        Sheets service = getSheetService(userEmail);
        List<String> errorMessages = new ArrayList<>();

        try {
            // Step 1: Get spreadsheet metadata to retrieve sheet names and IDs
            log.info("Retrieving spreadsheet metadata for: {}", sheetId);
            Spreadsheet spreadsheet = service.spreadsheets().get(sheetId).execute();

            List<Sheet> sheets = spreadsheet.getSheets();
            if (sheets == null || sheets.isEmpty()) {
                log.warn("No sheets found in spreadsheet: {}", sheetId);
                return SpreadsheetBatchResponseDto.builder()
                        .spreadsheetId(sheetId)
                        .sheets(new ArrayList<>())
                        .totalSheets(0)
                        .successfulSheets(0)
                        .allSheetsProcessed(true)
                        .errorMessages(new ArrayList<>())
                        .build();
            }

            // Step 2: Prepare batch request for all sheet data
            List<String> ranges = new ArrayList<>();
            for (Sheet sheet : sheets) {
                String sheetName = sheet.getProperties().getTitle();
                ranges.add(sheetName); // This will get all data from the sheet
            }

            log.info("Preparing batch request for {} sheets: {}", ranges.size(), ranges);

            // Step 3: Execute batch get request
            BatchGetValuesResponse batchResponse = service.spreadsheets().values()
                    .batchGet(sheetId)
                    .setRanges(ranges)
                    .execute();

            // Step 4: Process batch response and create result DTOs
            List<SheetBatchDataDto> sheetDataList = new ArrayList<>();
            List<ValueRange> valueRanges = batchResponse.getValueRanges();

            int successfulSheets = 0;
            for (int i = 0; i < sheets.size(); i++) {
                Sheet sheet = sheets.get(i);
                String sheetName = sheet.getProperties().getTitle();
                Integer sheetIdNum = sheet.getProperties().getSheetId();

                SheetBatchDataDto.SheetBatchDataDtoBuilder sheetDataBuilder = SheetBatchDataDto.builder()
                        .sheetName(sheetName)
                        .sheetId(sheetIdNum);

                // Check if we have corresponding data for this sheet
                if (i < valueRanges.size()) {
                    ValueRange valueRange = valueRanges.get(i);
                    List<List<Object>> data = valueRange.getValues();

                    sheetDataBuilder
                            .data(data != null ? data : new ArrayList<>())
                            .dataRetrieved(true);
                    successfulSheets++;

                    log.debug("Successfully retrieved data for sheet '{}' with {} rows",
                            sheetName, data != null ? data.size() : 0);
                } else {
                    // No data available for this sheet
                    String errorMsg = "No data retrieved for sheet: " + sheetName;
                    sheetDataBuilder
                            .data(new ArrayList<>())
                            .dataRetrieved(false)
                            .errorMessage(errorMsg);
                    errorMessages.add(errorMsg);

                    log.warn("No data retrieved for sheet: {}", sheetName);
                }

                sheetDataList.add(sheetDataBuilder.build());
            }

            log.info("Batch retrieval completed. Successfully processed {}/{} sheets",
                    successfulSheets, sheets.size());

            return SpreadsheetBatchResponseDto.builder()
                    .spreadsheetId(sheetId)
                    .sheets(sheetDataList)
                    .totalSheets(sheets.size())
                    .successfulSheets(successfulSheets)
                    .allSheetsProcessed(successfulSheets == sheets.size())
                    .errorMessages(errorMessages)
                    .build();

        } catch (Exception e) {
            log.error("Error during batch retrieval of sheets data for spreadsheet {}: {}", sheetId, e.getMessage(), e);
            errorMessages.add("Batch retrieval failed: " + e.getMessage());

            return SpreadsheetBatchResponseDto.builder()
                    .spreadsheetId(sheetId)
                    .sheets(new ArrayList<>())
                    .totalSheets(0)
                    .successfulSheets(0)
                    .allSheetsProcessed(false)
                    .errorMessages(errorMessages)
                    .build();
        }
    }

}
